import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');

  // 动态确定后端目标地址
  const getBackendTarget = () => {
    if (env.VITE_API_BASE_URL && env.VITE_API_BASE_URL.startsWith('http')) {
      // 服务器环境：从 VITE_API_BASE_URL 提取后端地址
      return env.VITE_API_BASE_URL.replace('/api', '');
    }
    // 本地环境：使用 localhost
    return "http://localhost:3000";
  };

  const backendTarget = getBackendTarget();

  return {
    plugins: [vue()],

    // 路径别名配置
    resolve: {
      alias: {
        "@": resolve(__dirname, "src"),
        "@components": resolve(__dirname, "src/components"),
        "@views": resolve(__dirname, "src/views"),
        "@api": resolve(__dirname, "src/api"),
        "@utils": resolve(__dirname, "src/utils"),
        "@composables": resolve(__dirname, "src/composables"),
        "@assets": resolve(__dirname, "src/assets"),
      },
    },

    // 开发服务器配置
    server: {
      host: "0.0.0.0",
      port: 5173,
      open: true,
      cors: true,
      // 优化 HMR 配置
      hmr: {
        overlay: true,
        clientPort: 5173,
      },
      // 文件监听配置
      watch: {
        usePolling: false,
        interval: 100,
        ignored: ['**/node_modules/**', '**/dist/**', '**/logs/**'],
      },
      // 代理配置 - 将 API 请求代理到后端服务器
      proxy: {
        "/api": {
          target: backendTarget,
          changeOrigin: true,
          secure: false,
          timeout: 10000,
        },
        "/uploads": {
          target: backendTarget,
          changeOrigin: true,
          secure: false,
          timeout: 10000,
        },
      },
    },

    // 预览服务器配置
    preview: {
      host: "0.0.0.0",
      port: 5173,
      cors: true,
      // 预览模式也需要代理配置
      proxy: {
        "/api": {
          target: backendTarget,
          changeOrigin: true,
          secure: false,
          timeout: 10000,
        },
        "/uploads": {
          target: backendTarget,
          changeOrigin: true,
          secure: false,
          timeout: 10000,
        },
      },
    },

    // 构建配置（开发环境优化）
    build: {
      outDir: "dist",
      assetsDir: "assets",
      sourcemap: true, // 开发环境启用 sourcemap
      minify: "terser", // 恢复压缩，提升性能
      chunkSizeWarningLimit: 2000,
      // 优化的代码分割配置
      rollupOptions: {
        output: {
          manualChunks: {
            // 将大型依赖分离到单独的 chunk
            'element-plus': ['element-plus'],
            'vue-vendor': ['vue', 'vue-router'],
            'utils': ['axios', 'pinia'],
          },
        },
      },
    },

    // CSS 配置
    css: {
      postcss: "./postcss.config.js",
      devSourcemap: true, // 开发环境 CSS sourcemap
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables.scss";`,
          charset: false,
        },
      },
    },

    // 依赖预构建优化配置
    optimizeDeps: {
      // 强制预构建的依赖
      include: [
        "vue",
        "vue-router",
        "element-plus",
        "element-plus/es",
        "@element-plus/icons-vue",
        "axios",
        "pinia",
        "cropperjs",
        "echarts",
        "vue-echarts",
        "pdfjs-dist",
        "vue-pdf-embed",
      ],
      // 排除预构建的依赖
      exclude: [
        "sass-embedded",
      ],
      // 强制重新构建依赖
      force: false, // 设为 true 可强制重新构建
      // 预构建缓存目录
      cacheDir: "node_modules/.vite",
    },

    // 缓存配置
    cacheDir: "node_modules/.vite",

    // 日志级别
    logLevel: "info",

    // 清除控制台
    clearScreen: false,
  };
});
