/**
 * 应用配置文件
 * 统一管理API配置和其他应用设置
 */

// 动态获取API基础URL
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL;
  }

  // 开发环境使用代理
  if (import.meta.env.DEV) {
    return "/api";
  }

  // 生产环境动态获取当前域名
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = "3000"; // 后端端口

  return `${protocol}//${hostname}:${port}/api`;
};

// API配置
export const API_CONFIG = {
  BASE_URL: getApiBaseUrl(),
  TIMEOUT: 10000,
};

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  AVATAR_MAX_SIZE: 2 * 1024 * 1024, // 2MB for avatars
};

// 生成完整的API URL
export const getApiUrl = (path) => {
  return `${API_CONFIG.BASE_URL}${path.startsWith("/") ? path : "/" + path}`;
};

// 生成头像URL
export const getAvatarUrl = (avatarPath) => {
  if (!avatarPath) return null;
  if (avatarPath.startsWith("http")) return avatarPath;

  // 检查是否为服务器部署环境
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
  const isServerDeployment = apiBaseUrl && apiBaseUrl.startsWith("http");

  if (isServerDeployment) {
    // 服务器环境：使用完整的后端URL
    const backendBaseUrl = apiBaseUrl.replace('/api', '');
    const cleanPath = avatarPath.startsWith("/") ? avatarPath : "/" + avatarPath;
    return `${backendBaseUrl}${cleanPath}`;
  } else {
    // 本地环境：头像文件通过静态文件服务访问，使用相对路径通过Vite代理
    return avatarPath.startsWith("/") ? avatarPath : "/" + avatarPath;
  }
};
