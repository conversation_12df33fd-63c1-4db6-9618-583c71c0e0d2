<template>
  <div class="contract-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          合同详情 - {{ contract?.serial_number || "加载中..." }}
        </h1>
        <p class="page-subtitle">查看合同的详细信息和文件内容</p>
      </div>
      <div class="header-right">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon" :size="48">
        <Loading />
      </el-icon>
      <p class="loading-text">正在加载合同详情...</p>
    </div>

    <!-- 主要内容 -->
    <div v-else-if="contract" class="page-content">
      <!-- 合同信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <el-icon><Document /></el-icon>
              <span>合同信息</span>
            </div>
            <div class="status-badge">
              <el-tag :type="getStatusColor(contract.status)" size="large">
                {{ formatStatus(contract.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">流水号：</label>
            <span class="info-value">{{ contract.serial_number }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">文件名：</label>
            <span class="info-value" :title="contract.filename">{{
              contract.filename
            }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">文件大小：</label>
            <span class="info-value">{{
              formatFileSize(contract.file_size)
            }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">提交人：</label>
            <span class="info-value">{{ contract.submitter_name }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">审核人：</label>
            <span class="info-value">{{
              contract.reviewer_name || "未分配"
            }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">提交时间：</label>
            <span class="info-value">{{
              formatDateTime(contract.created_at)
            }}</span>
          </div>

          <div class="info-item full-width">
            <label class="info-label">提交说明：</label>
            <span class="info-value">{{ contract.submit_note || "无" }}</span>
          </div>

          <!-- 审核时间 -->
          <div v-if="contract.reviewed_at" class="info-item">
            <label class="info-label">审核时间：</label>
            <span class="info-value">{{
              formatDateTime(contract.reviewed_at)
            }}</span>
          </div>
        </div>

        <!-- 审核意见区域 -->
        <div v-if="contract.review_comment" class="review-section">
          <h4 class="review-title">
            <el-icon><ChatDotRound /></el-icon>
            审核意见
          </h4>
          <div
            class="review-comment"
            :class="`review-comment--${contract.status}`"
          >
            {{ contract.review_comment }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button
            v-if="canModify(contract)"
            type="warning"
            @click="editContract"
          >
            <el-icon><Edit /></el-icon>
            修改合同
          </el-button>

          <el-button
            v-if="canReview(contract) && contract.status === 'pending'"
            type="success"
            :loading="startingReview"
            @click="startReviewAction"
          >
            <el-icon><Check /></el-icon>
            开始审核
          </el-button>
        </div>
      </el-card>

      <!-- 审核操作卡片 - 只在审核中状态显示 -->
      <el-card
        v-if="canReview(contract) && contract.status === 'reviewing'"
        class="review-card"
        shadow="hover"
      >
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <el-icon><EditPen /></el-icon>
              <span>审核操作</span>
            </div>
          </div>
        </template>

        <div class="review-form">
          <el-form
            ref="reviewFormRef"
            :model="reviewForm"
            :rules="reviewRules"
            label-width="100px"
            label-position="top"
          >
            <el-form-item label="审核结果" prop="result">
              <el-radio-group v-model="reviewForm.result" size="large">
                <el-radio value="approved" border>
                  <el-icon color="#67c23a"><CircleCheckFilled /></el-icon>
                  <span style="margin-left: 8px">通过</span>
                </el-radio>
                <el-radio value="rejected" border>
                  <el-icon color="#f56c6c"><CircleCloseFilled /></el-icon>
                  <span style="margin-left: 8px">拒绝</span>
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="审核意见" prop="comment">
              <el-input
                v-model="reviewForm.comment"
                type="textarea"
                :rows="4"
                placeholder="请输入审核意见（至少10个字符）"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="submittingReview"
                :disabled="!reviewForm.result || !reviewForm.comment"
                @click="submitReviewAction"
              >
                <el-icon><Check /></el-icon>
                提交审核结果
              </el-button>
              <el-button @click="resetReviewForm">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- PDF 预览区域 - 独立显示在页面底部 -->
    <div v-if="contract && previewUrl" class="pdf-preview-section">
      <div class="preview-header">
        <div class="preview-title">
          <el-icon><View /></el-icon>
          <span>文件预览</span>
        </div>
      </div>

      <div class="preview-content">
        <SimplePDFViewer
          :src="previewUrl"
          :filename="contract.filename"
          @load="handlePreviewLoad"
          @error="handlePreviewError"
        />
      </div>
    </div>

    <!-- PDF 预览错误状态 -->
    <div v-else-if="contract && !previewUrl" class="pdf-preview-section">
      <div class="preview-header">
        <div class="preview-title">
          <el-icon><View /></el-icon>
          <span>文件预览</span>
        </div>
      </div>

      <div class="preview-placeholder">
        <el-icon :size="64" class="placeholder-icon">
          <DocumentCopy />
        </el-icon>
        <p class="placeholder-text">无法预览此文件</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-icon :size="64" class="error-icon">
        <Warning />
      </el-icon>
      <p class="error-text">合同不存在或已被删除</p>
      <el-button @click="goBack">返回</el-button>
    </div>

    <!-- 编辑对话框 -->
    <ContractEditDialog
      v-model="showEditDialog"
      :contract="contract"
      @updated="handleContractUpdated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";

// 定义props
const props = defineProps({
  contractId: {
    type: [String, Number],
    default: null,
  },
  contract: {
    type: Object,
    default: null,
  },
});
import {
  Loading,
  Document,
  ArrowLeft,
  Edit,
  Check,
  View,
  DocumentCopy,
  Warning,
  EditPen,
  CircleCheckFilled,
  CircleCloseFilled,
  RefreshLeft,
  ChatDotRound,
} from "@element-plus/icons-vue";

import SimplePDFViewer from "@/components/common/SimplePDFViewer.vue";
import ContractEditDialog from "@/components/contract/ContractEditDialog.vue";
import { useContracts } from "@/composables/useContracts";
import { filesAPI } from "@/api/files";

const route = useRoute();
const router = useRouter();

// 合同管理
const {
  getContractDetail,
  canModify,
  canReview,
  formatStatus,
  getStatusColor,
  formatFileSize,
  formatDateTime,
  startReview,
  submitReview,
  submitting,
} = useContracts();

// 组件状态
const loading = ref(false);
const contract = ref(null); // 总是从API加载最新数据

// 编辑相关状态
const showEditDialog = ref(false);

// 审核相关状态
const startingReview = ref(false);
const submittingReview = ref(false);
const reviewFormRef = ref();

// 审核表单
const reviewForm = ref({
  result: "",
  comment: "",
});

// 动态审核表单验证规则
const reviewRules = computed(() => {
  const rules = {
    result: [{ required: true, message: "请选择审核结果", trigger: "change" }],
    comment: []
  };

  // 根据审核结果动态设置comment验证规则
  if (reviewForm.value.result === "rejected") {
    // 审核驳回时，说明必填且至少10个字符
    rules.comment = [
      { required: true, message: "请填写审核意见", trigger: "blur" },
      { min: 10, message: "审核意见至少10个字符", trigger: "blur" },
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else if (reviewForm.value.result === "approved") {
    // 审核通过时，说明可选
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else {
    // 未选择审核结果时，暂不验证comment
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  }

  return rules;
});

// 获取合同ID - 优先使用props，然后是路由参数
const contractId = computed(() => props.contractId || route.params.id);

// 预览相关 - 使用不带token的URL，让PDFViewer自动添加token
const previewUrl = computed(() => {
  if (contract.value && contract.value.id) {
    return filesAPI.getContractPreviewUrl(contract.value.id);
  }
  return null;
});

// 监听路由参数变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      loadContractDetail();
    }
  },
  { immediate: true },
);

// 加载合同详情 - 使用统一错误处理
const loadContractDetail = async () => {
  // 总是重新加载合同详情，确保数据完整性
  loading.value = true;
  try {
    const result = await getContractDetail(contractId.value);
    contract.value = result;
  } finally {
    loading.value = false;
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 编辑合同
const editContract = () => {
  showEditDialog.value = true;
};

// 处理合同更新
const handleContractUpdated = (updatedContract) => {
  contract.value = updatedContract;
  ElMessage.success("合同修改成功");
};

// 开始审核
const startReviewAction = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要开始审核这个合同吗？开始后合同状态将变为"审核中"。',
      "确认开始审核",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      },
    );

    startingReview.value = true;
    const result = await startReview(contract.value.id);

    if (result) {
      contract.value = result;
      ElMessage.success("开始审核成功");
    }
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("开始审核失败:", error);
      }
    }
  } finally {
    startingReview.value = false;
  }
};

// 提交审核结果
const submitReviewAction = async () => {
  try {
    // 验证表单
    await reviewFormRef.value.validate();

    const resultText = reviewForm.value.result === "approved" ? "通过" : "拒绝";
    await ElMessageBox.confirm(
      `确定要${resultText}这个合同吗？提交后无法修改审核结果。`,
      "确认提交审核结果",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    submittingReview.value = true;
    const reviewData = {
      result: reviewForm.value.result,
      comment: reviewForm.value.comment,
    };

    const result = await submitReview(contract.value.id, reviewData);

    if (result) {
      contract.value = result;
      resetReviewForm();
      ElMessage.success(`审核${resultText}成功`);
    }
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("提交审核失败:", error);
      }
    }
  } finally {
    submittingReview.value = false;
  }
};

// 重置审核表单
const resetReviewForm = () => {
  reviewForm.value = {
    result: "",
    comment: "",
  };
  reviewFormRef.value?.clearValidate();
};

// 处理预览加载
const handlePreviewLoad = (info) => {};

// 处理预览错误
const handlePreviewError = (error) => {
  if (import.meta.env.DEV) {
    console.error("PDF 预览失败:", error);
  }
  ElMessage.warning("PDF 预览失败，请尝试下载文件查看");
};

// 组件挂载时加载数据
onMounted(() => {
  console.log("🔍 合同详情页面调试信息:");
  console.log("props.contractId:", props.contractId);
  console.log("route.params.id:", route.params.id);
  console.log("contractId.value:", contractId.value);
  console.log("props.contract:", props.contract);

  if (contractId.value) {
    loadContractDetail();
  } else {
    console.warn("⚠️ contractId为空，无法加载合同详情");
  }
});
</script>

<style scoped>
.contract-detail-page {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-icon {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.loading-text {
  margin-top: 16px;
  color: #909399;
  font-size: 14px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 页面内容 */
.page-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.info-value {
  color: #303133;
  word-break: break-all;
  flex: 1;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

/* 审核卡片 */
.review-card {
  margin-top: 20px;
}

.review-form {
  padding: 0;
}

.review-form .el-radio {
  margin-right: 20px;
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 6px;
  transition: all 0.3s;
}

.review-form .el-radio.is-checked {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.review-form .el-radio:hover {
  background-color: var(--el-color-primary-light-9);
}

.review-form .el-textarea {
  margin-bottom: 16px;
}

.review-form .el-form-item:last-child {
  margin-bottom: 0;
}

/* 预览容器 */
.preview-container {
  min-height: 700px;
  border-radius: 4px;
  overflow: hidden;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #fafafa;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
}

.placeholder-icon {
  color: #c0c4cc;
  margin-bottom: 16px;
}

.placeholder-text {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

/* PDF 预览区域样式 */
.pdf-preview-section {
  margin-top: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  padding: 0;
  background: #fff;
}

.pdf-preview-section .preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
  background: #fafafa;
}

/* 审核意见区域 */
.review-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.review-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.review-comment {
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

.review-comment--approved {
  border-left: 4px solid #67c23a;
  background: #f0f9ff;
}

.review-comment--rejected {
  border-left: 4px solid #f56c6c;
  background: #fef0f0;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error-icon {
  color: #f56c6c;
  margin-bottom: 16px;
}

.error-text {
  color: #909399;
  font-size: 16px;
  margin: 0 0 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contract-detail-page {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    justify-content: center;
  }
}
</style>
