<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`审核合同 - ${contract?.serial_number || ''}`"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="review-dialog"
    @close="handleClose"
  >
    <div v-if="contract" class="review-content">
      <!-- 合同信息面板 -->
      <div class="contract-info-panel">
        <h3 class="panel-title">
          <el-icon><Document /></el-icon>
          合同信息
        </h3>

        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">流水号：</span>
            <span class="info-value">{{ contract.serial_number }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">文件名：</span>
            <span class="info-value" :title="contract.filename">{{
              contract.filename
            }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">提交人：</span>
            <span class="info-value">{{ contract.submitter_name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">提交时间：</span>
            <span class="info-value">{{
              formatDateTime(contract.created_at)
            }}</span>
          </div>
          <div v-if="contract.submit_note" class="info-item">
            <span class="info-label">提交说明：</span>
            <span class="info-value">{{ contract.submit_note }}</span>
          </div>
        </div>
      </div>

      <!-- PDF 预览面板 -->
      <div class="preview-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <el-icon><View /></el-icon>
            文件预览
          </h3>
        </div>

        <div class="preview-container">
          <SimplePDFViewer
            v-if="previewUrl"
            :src="previewUrl"
            :filename="contract.filename"
            @load="handlePreviewLoad"
            @error="handlePreviewError"
          />

          <div v-else class="preview-placeholder">
            <el-icon :size="64" class="placeholder-icon">
              <DocumentCopy />
            </el-icon>
            <p class="placeholder-text">无法预览此文件</p>
          </div>
        </div>
      </div>

      <!-- 审核表单面板 -->
      <div class="review-form-panel">
        <h3 class="panel-title">
          <el-icon><EditPen /></el-icon>
          审核意见
        </h3>

        <el-form
          ref="reviewFormRef"
          :model="reviewForm"
          :rules="reviewRules"
          label-width="100px"
          size="default"
        >
          <el-form-item label="审核结果" prop="result" required>
            <el-radio-group v-model="reviewForm.result">
              <el-radio value="approved" size="large">
                <el-icon class="result-icon approved"
                  ><CircleCheckFilled
                /></el-icon>
                通过
              </el-radio>
              <el-radio value="rejected" size="large">
                <el-icon class="result-icon rejected"
                  ><CircleCloseFilled
                /></el-icon>
                拒绝
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="审核意见" prop="comment" required>
            <el-input
              v-model="reviewForm.comment"
              type="textarea"
              :rows="6"
              :placeholder="getCommentPlaceholder()"
              maxlength="1000"
              show-word-limit
            />
            <div class="form-tips">
              <el-icon><InfoFilled /></el-icon>
              <span>请详细说明审核理由，便于提交人了解情况</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="submitting" @click="handleClose">
          取消
        </el-button>
        <el-button
          v-if="contract?.status === 'pending'"
          type="success"
          :loading="submitting"
          :disabled="!reviewForm.result || (reviewForm.result === 'rejected' && !reviewForm.comment)"
          @click="submitReview"
        >
          {{ submitting ? "提交中..." : "提交审核结果" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Document,
  View,
  EditPen,
  DocumentCopy,
  InfoFilled,
  CircleCheckFilled,
  CircleCloseFilled,
} from "@element-plus/icons-vue";

import SimplePDFViewer from "@/components/common/SimplePDFViewer.vue";
import { useContracts } from "@/composables/useContracts";
import { filesAPI } from "@/api/files";

// 定义 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  contract: {
    type: Object,
    default: null,
  },
});

// 定义 emits
const emit = defineEmits(["update:modelValue", "reviewed"]);

// 合同管理
const {
  submitReview: submitReviewAPI,
  submitting,
  formatDateTime,
} = useContracts();

// 对话框显示状态
const dialogVisible = ref(props.modelValue);

// 表单引用
const reviewFormRef = ref();

// 组件状态

// 审核表单数据
const reviewForm = reactive({
  result: "",
  comment: "",
});

// 动态表单验证规则
const reviewRules = computed(() => {
  const rules = {
    result: [{ required: true, message: "请选择审核结果", trigger: "change" }],
    comment: []
  };

  // 根据审核结果动态设置comment验证规则
  if (reviewForm.result === "rejected") {
    // 审核驳回时，说明必填且至少10个字符
    rules.comment = [
      { required: true, message: "请填写审核意见", trigger: "blur" },
      { min: 10, message: "审核意见至少10个字符", trigger: "blur" },
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else if (reviewForm.result === "approved") {
    // 审核通过时，说明可选
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else {
    // 未选择审核结果时，暂不验证comment
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  }

  return rules;
});

// 预览URL - 使用不带token的URL，让SimplePDFViewer自动添加token
const previewUrl = computed(() => {
  if (props.contract && props.contract.id) {
    return filesAPI.getContractPreviewUrl(props.contract.id);
  }
  return null;
});

// 带token的预览URL，用于全屏预览
const authenticatedPreviewUrl = computed(() => {
  if (props.contract && props.contract.id) {
    const baseURL = import.meta.env.VITE_API_BASE_URL || "/api";
    const token = localStorage.getItem("token");
    if (token) {
      return `${baseURL}/files/${props.contract.id}/preview?token=${token}`;
    }
    return `${baseURL}/files/${props.contract.id}/preview`;
  }
  return null;
});

// 监听 props 变化
watch(
  () => props.modelValue,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);

watch(
  () => props.contract,
  (newContract) => {
    if (newContract) {
      resetForm();
    }
  },
);

// 监听对话框显示状态
watch(
  () => dialogVisible.value,
  (newValue) => {
    emit("update:modelValue", newValue);

    if (!newValue) {
      resetForm();
    }
  },
);

// 重置表单
const resetForm = () => {
  reviewForm.result = "";
  reviewForm.comment = "";

  if (reviewFormRef.value) {
    reviewFormRef.value.clearValidate();
  }
};

// 获取评论占位符
const getCommentPlaceholder = () => {
  if (reviewForm.result === "approved") {
    return "请说明通过的理由，如：合同条款清晰，内容符合要求...";
  } else if (reviewForm.result === "rejected") {
    return "请详细说明拒绝的原因，如：合同条款不明确，缺少必要信息...";
  }
  return "请填写详细的审核意见";
};



// 提交审核结果
const submitReview = async () => {
  try {
    // 验证表单
    await reviewFormRef.value.validate();

    const resultText = reviewForm.result === "approved" ? "通过" : "拒绝";
    await ElMessageBox.confirm(
      `确定要${resultText}这个合同吗？提交后无法修改审核结果。`,
      "确认提交审核结果",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    const reviewData = {
      result: reviewForm.result,
      comment: reviewForm.comment,
    };

    const result = await submitReviewAPI(props.contract.id, reviewData);

    if (result) {
      // 发送审核完成事件，传递更新后的合同数据
      emit("reviewed", result);
      dialogVisible.value = false;

      // 显示成功消息
      const resultText = reviewForm.result === "approved" ? "通过" : "拒绝";
      ElMessage.success(`审核${resultText}成功`);
    }
  } catch (error) {
    // 只处理用户取消，API错误由全局处理器处理
    if (error === "cancel") {
      // 用户取消操作，不需要处理
    }
  }
};

// 处理预览加载
const handlePreviewLoad = (info) => {};

// 处理预览错误 - 保留用户友好提示，移除重复日志
const handlePreviewError = (error) => {
  ElMessage.warning("PDF 预览失败，请尝试下载文件查看");
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.review-dialog {
  --el-dialog-width: 80%;
}

.review-content {
  display: grid;
  grid-template-columns: 300px 1fr 350px;
  gap: 20px;
  height: 80vh;
}

.contract-info-panel,
.preview-panel,
.review-form-panel {
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.contract-info-panel {
  overflow-y: auto;
}

.info-grid {
  padding: 20px;
  display: grid;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-weight: 500;
  color: #606266;
  font-size: 12px;
}

.info-value {
  color: #303133;
  word-break: break-all;
  font-size: 14px;
}

.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.preview-panel :deep(.el-card__body) {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.placeholder-icon {
  margin-bottom: 16px;
}

.placeholder-text {
  font-size: 14px;
  margin: 0;
}

.review-form-panel {
  overflow-y: auto;
}

.review-form-panel .el-form {
  padding: 20px;
}

.result-icon {
  margin-right: 8px;
}

.result-icon.approved {
  color: #67c23a;
}

.result-icon.rejected {
  color: #f56c6c;
}

.form-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.form-tips .el-icon {
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Element Plus 样式覆盖 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-radio) {
  margin-right: 24px;
  margin-bottom: 12px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .review-content {
    grid-template-columns: 280px 1fr 320px;
    gap: 16px;
  }
}

@media (max-width: 1200px) {
  .review-dialog {
    --el-dialog-width: 95%;
  }

  .review-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
    height: auto;
  }

  .contract-info-panel,
  .review-form-panel {
    max-height: 300px;
  }

  .preview-container {
    height: 500px;
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .review-content {
    gap: 12px;
  }

  .panel-title {
    padding: 12px 16px;
    font-size: 14px;
  }

  .info-grid {
    padding: 16px;
    gap: 12px;
  }

  .preview-container {
    padding: 16px;
    height: 400px;
    min-height: 400px;
  }

  .review-form-panel .el-form {
    padding: 16px;
  }
}
</style>
