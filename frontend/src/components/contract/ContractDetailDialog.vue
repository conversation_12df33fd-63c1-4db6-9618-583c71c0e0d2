<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`合同详情 - ${contract?.serial_number || ''}`"
    width="90%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="contract-detail-dialog"
    @close="handleClose"
  >
    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon" :size="48">
        <Loading />
      </el-icon>
      <p class="loading-text">正在加载合同详情...</p>
    </div>

    <div v-else-if="contract" class="detail-content">
      <!-- 左侧信息和操作面板 -->
      <div class="left-panel">
        <!-- 合同信息面板 -->
        <div class="info-panel">
          <div class="panel-header">
            <h3 class="panel-title">
              <el-icon><Document /></el-icon>
              合同信息
            </h3>
            <div class="status-badge">
              <el-tag :type="getStatusColor(contract.status)" size="large">
                {{ formatStatus(contract.status) }}
              </el-tag>
            </div>
          </div>

          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">流水号：</span>
              <span class="info-value">{{ contract.serial_number }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">文件名：</span>
              <span class="info-value" :title="contract.filename">{{
                contract.filename
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">文件大小：</span>
              <span class="info-value">{{
                formatFileSize(contract.file_size)
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">提交人：</span>
              <span class="info-value">{{ contract.submitter_name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">审核人：</span>
              <span class="info-value">{{ contract.reviewer_name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">提交时间：</span>
              <span class="info-value">{{
                formatDateTime(contract.created_at)
              }}</span>
            </div>
            <div v-if="contract.reviewed_at" class="info-item">
              <span class="info-label">审核时间：</span>
              <span class="info-value">{{
                formatDateTime(contract.reviewed_at)
              }}</span>
            </div>
            <div v-if="contract.submit_note" class="info-item">
              <span class="info-label">提交说明：</span>
              <span class="info-value">{{ contract.submit_note }}</span>
            </div>
          </div>

          <!-- 审核意见 -->
          <div v-if="contract.review_comment" class="review-section">
            <h4 class="review-title">
              <el-icon><ChatDotRound /></el-icon>
              审核意见
            </h4>
            <div
              class="review-comment"
              :class="`review-comment--${contract.status}`"
            >
              {{ contract.review_comment }}
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button
              v-if="debugCanModify"
              type="warning"
              @click="editContract"
            >
              <el-icon><Edit /></el-icon>
              修改合同
            </el-button>

            <el-button type="primary" @click="openInNewTab">
              <el-icon><FullScreen /></el-icon>
              在新Tab中打开
            </el-button>
          </div>
        </div>

        <!-- 审核操作面板 -->
        <div
          v-if="canReview(contract) && contract.status === 'pending'"
          class="review-panel"
        >
          <div class="panel-header">
            <h3 class="panel-title">
              <el-icon><EditPen /></el-icon>
              审核操作
            </h3>
          </div>

          <div class="review-form">
            <el-form
              ref="reviewFormRef"
              :model="reviewForm"
              :rules="reviewRules"
              label-width="80px"
            >
              <el-form-item label="审核结果" prop="result">
                <el-radio-group v-model="reviewForm.result" size="default">
                  <el-radio value="approved" border>
                    <el-icon color="#67c23a"><CircleCheckFilled /></el-icon>
                    <span style="margin-left: 8px">通过</span>
                  </el-radio>
                  <el-radio value="rejected" border>
                    <el-icon color="#f56c6c"><CircleCloseFilled /></el-icon>
                    <span style="margin-left: 8px">拒绝</span>
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="审核意见" prop="comment">
                <el-input
                  v-model="reviewForm.comment"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入审核意见（至少10个字符）"
                  maxlength="1000"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  :loading="submittingReview"
                  :disabled="!reviewForm.result || (reviewForm.result === 'rejected' && !reviewForm.comment)"
                  @click="submitReviewAction"
                >
                  <el-icon><Check /></el-icon>
                  提交审核结果
                </el-button>
                <el-button @click="resetReviewForm">
                  <el-icon><RefreshLeft /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>

      <!-- 右侧PDF预览面板 -->
      <div class="preview-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <el-icon><View /></el-icon>
            文件预览
          </h3>
        </div>

        <div class="preview-container">
          <SimplePDFViewer
            v-if="previewUrl"
            ref="pdfViewerRef"
            :src="previewUrl"
            :filename="contract.filename"
            @load="handlePreviewLoad"
            @error="handlePreviewError"
          />

          <div v-else class="preview-placeholder">
            <el-icon :size="64" class="placeholder-icon">
              <DocumentCopy />
            </el-icon>
            <p class="placeholder-text">无法预览此文件</p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Loading,
  Document,
  ChatDotRound,
  Edit,
  Check,
  View,
  DocumentCopy,
  FullScreen,
  EditPen,
  CircleCheckFilled,
  CircleCloseFilled,
  RefreshLeft,
} from "@element-plus/icons-vue";

import SimplePDFViewer from "@/components/common/SimplePDFViewer.vue";
import { useContracts } from "@/composables/useContracts";
import { useTabs } from "@/composables/useTabs";
import { filesAPI } from "@/api/files";
import api from "@/api/request";

// 定义 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  contractId: {
    type: [Number, String],
    default: null,
  },
});

// 定义 emits
const emit = defineEmits(["update:modelValue", "edit", "review", "updated"]);

// 合同管理
const {
  getContractDetail,
  canModify,
  canReview,
  formatStatus,
  getStatusColor,
  formatFileSize,
  formatDateTime,
  submitReview,
  submitting,
  user, // 从 useContracts 中获取用户信息
} = useContracts();

// Tab管理
const { openContractDetailTab } = useTabs();

// 对话框显示状态
const dialogVisible = ref(props.modelValue);

// 组件状态
const loading = ref(false);
const contract = ref(null);

// 审核相关状态
const submittingReview = ref(false);
const reviewFormRef = ref();

// 审核表单
const reviewForm = ref({
  result: "",
  comment: "",
});

// 动态审核表单验证规则
const reviewRules = computed(() => {
  const rules = {
    result: [{ required: true, message: "请选择审核结果", trigger: "change" }],
    comment: []
  };

  // 根据审核结果动态设置comment验证规则
  if (reviewForm.value.result === "rejected") {
    // 审核驳回时，说明必填且至少10个字符
    rules.comment = [
      { required: true, message: "请填写审核意见", trigger: "blur" },
      { min: 10, message: "审核意见至少10个字符", trigger: "blur" },
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else if (reviewForm.value.result === "approved") {
    // 审核通过时，说明可选
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else {
    // 未选择审核结果时，暂不验证comment
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  }

  return rules;
});

// PDF预加载缓存
const previewCache = new Map();

// 预览相关 - 使用统一API配置
const previewUrl = computed(() => {
  if (contract.value && contract.value.id) {
    // 使用统一的API配置，让拦截器自动处理token
    const baseURL = import.meta.env.VITE_API_BASE_URL || "/api";
    return `${baseURL}/files/${contract.value.id}/preview`;
  }
  return null;
});

// 带token的预览URL，用于全屏预览
const authenticatedPreviewUrl = computed(() => {
  if (previewUrl.value) {
    const token = localStorage.getItem("token");
    if (token) {
      const separator = previewUrl.value.includes("?") ? "&" : "?";
      return `${previewUrl.value}${separator}token=${token}`;
    }
  }
  return previewUrl.value;
});

// 预加载PDF文件 - 使用统一API服务
const preloadPDF = async (contractId) => {
  if (!contractId || previewCache.has(contractId)) return;

  try {
    // 使用统一的API服务进行HEAD请求检查文件是否存在
    await api.get(
      `/files/${contractId}/preview`,
      {},
      {
        method: "HEAD",
        showLoading: false, // 预加载不显示加载状态
      },
    );

    previewCache.set(contractId, true);
  } catch (err) {
    // 预加载失败是非关键错误，静默处理即可
    // 全局错误处理器会处理网络错误，这里不需要额外处理
  }
};

// 监听 props 变化
watch(
  () => props.modelValue,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);

watch(
  () => props.contractId,
  (newId) => {
    if (newId && dialogVisible.value) {
      loadContractDetail();
    }
  },
);

// 监听对话框显示状态
watch(
  () => dialogVisible.value,
  (newValue) => {
    emit("update:modelValue", newValue);

    if (newValue && props.contractId) {
      loadContractDetail();

      // 预加载PDF文件
      setTimeout(() => {
        if (props.contractId) {
          preloadPDF(props.contractId);
        }
      }, 100);
    } else {
      contract.value = null;
    }
  },
);

// 加载合同详情 - 简化错误处理，依赖全局错误处理器
const loadContractDetail = async () => {
  loading.value = true;
  try {
    const result = await getContractDetail(props.contractId);
    contract.value = result;
  } finally {
    loading.value = false;
  }
};

// 编辑合同
const editContract = () => {
  emit("edit", contract.value);
};

// 调试修改权限
const debugCanModify = computed(() => {
  const result = canModify(contract.value);
  return result;
});

// 审核合同
const reviewContract = () => {
  emit("review", contract.value);
};

// 在新Tab中打开
const openInNewTab = () => {
  if (contract.value) {
    openContractDetailTab(contract.value);
    // 关闭当前对话框
    handleClose();
  }
};

// 提交审核结果
const submitReviewAction = async () => {
  try {
    // 验证表单
    await reviewFormRef.value.validate();

    const resultText = reviewForm.value.result === "approved" ? "通过" : "拒绝";
    await ElMessageBox.confirm(
      `确定要${resultText}这个合同吗？提交后无法修改审核结果。`,
      "确认提交审核结果",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    submittingReview.value = true;
    const reviewData = {
      result: reviewForm.value.result,
      comment: reviewForm.value.comment,
    };

    const result = await submitReview(contract.value.id, reviewData);

    if (result) {
      contract.value = result;
      emit("updated", result);
      resetReviewForm();
      ElMessage.success(`审核${resultText}成功`);
      // 审核完成后关闭对话框
      handleClose();
    }
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("提交审核失败:", error);
      }
    }
  } finally {
    submittingReview.value = false;
  }
};

// 重置审核表单
const resetReviewForm = () => {
  reviewForm.value = {
    result: "",
    comment: "",
  };
  reviewFormRef.value?.clearValidate();
};

// PDF查看器引用
const pdfViewerRef = ref();

// 处理预览加载
const handlePreviewLoad = (info) => {};

// 处理预览错误 - 保留用户友好提示，移除重复日志
const handlePreviewError = (error) => {
  // PDF预览错误通常不是致命错误，给用户友好提示即可
  ElMessage.warning("PDF 预览失败，请尝试下载文件查看");
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};

// 组件挂载时加载数据
onMounted(() => {
  if (dialogVisible.value && props.contractId) {
    loadContractDetail();
  }
});
</script>

<style scoped>
.contract-detail-dialog {
  --el-dialog-width: 95%;
  --el-dialog-margin-top: 5vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
}

.loading-icon {
  margin-bottom: 16px;
}

.loading-text {
  font-size: 14px;
  margin: 0;
}

.detail-content {
  display: grid;
  grid-template-columns: 420px 1fr;
  gap: 20px;
  height: 80vh;
  max-height: 900px;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 100%;
  overflow-y: auto;
}

.info-panel,
.review-panel,
.preview-panel {
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.info-panel {
  flex: 1;
  min-height: 0;
}

.review-panel {
  flex-shrink: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.status-badge {
  display: flex;
  align-items: center;
}

.info-panel {
  overflow-y: auto;
}

.info-grid {
  padding: 20px;
  display: grid;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
  min-width: 80px;
}

.info-value {
  color: #303133;
  word-break: break-all;
  flex: 1;
}

.review-section {
  margin: 0 20px 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}

.review-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px;
}

.review-comment {
  padding: 12px;
  border-radius: 6px;
  line-height: 1.6;
  color: #303133;
}

.review-comment--approved {
  background: #f0f9ff;
  border: 1px solid #c2e7b0;
}

.review-comment--rejected {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
}

.action-buttons {
  padding: 20px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 审核面板 */
.review-panel {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.panel-header {
  margin-bottom: 16px;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.review-form .el-radio {
  margin-right: 16px;
  margin-bottom: 12px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
}

.review-form .el-radio.is-checked {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.review-form .el-radio:hover {
  background-color: var(--el-color-primary-light-9);
}

.review-form .el-form-item:last-child {
  margin-bottom: 0;
}

.preview-panel {
  flex: 1;
  min-height: 0;
  position: relative;
}

.preview-container {
  flex: 1;
  padding: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.placeholder-icon {
  margin-bottom: 16px;
}

.placeholder-text {
  font-size: 14px;
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* Element Plus 样式覆盖 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .detail-content {
    grid-template-columns: 380px 1fr;
  }
}

@media (max-width: 1200px) {
  .detail-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    height: auto;
    gap: 16px;
  }

  .left-panel {
    max-height: 50vh;
    overflow-y: auto;
  }

  .preview-container {
    height: 60vh;
  }
}

@media (max-width: 768px) {
  .contract-detail-dialog {
    --el-dialog-width: 98%;
    --el-dialog-margin-top: 2vh;
  }

  .detail-content {
    gap: 12px;
    height: 85vh;
  }

  .panel-header {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .info-grid {
    padding: 16px;
    gap: 12px;
  }

  .info-item {
    flex-direction: column;
    gap: 4px;
  }

  .info-label {
    min-width: auto;
  }

  .action-buttons {
    padding: 16px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .preview-container {
    padding: 12px;
    height: 50vh;
  }

  .left-panel {
    max-height: 40vh;
  }
}
</style>
