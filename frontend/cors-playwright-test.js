/**
 * Playwright CORS 跨域测试脚本
 * 自动化测试前后端 CORS 配置
 */

import { chromium } from 'playwright';

class CORSPlaywrightTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.frontendUrl = 'http://localhost:5173';
    this.backendUrl = 'http://localhost:3000';
  }

  async init() {
    console.log('🚀 启动 Playwright 浏览器...');
    this.browser = await chromium.launch({ 
      headless: false, // 显示浏览器窗口
      slowMo: 1000 // 减慢操作速度便于观察
    });
    this.page = await this.browser.newPage();
    
    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      if (type === 'error') {
        console.log(`❌ 浏览器控制台错误: ${msg.text()}`);
      } else if (type === 'warning') {
        console.log(`⚠️  浏览器控制台警告: ${msg.text()}`);
      }
    });

    // 监听网络请求
    this.page.on('request', request => {
      if (request.url().includes('/api/')) {
        console.log(`📡 API请求: ${request.method()} ${request.url()}`);
      }
    });

    // 监听网络响应
    this.page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log(`📨 API响应: ${response.status()} ${response.url()}`);
      }
    });
  }

  async testCORSTestPage() {
    console.log('\n🔍 测试 CORS 测试页面...');
    
    try {
      // 访问 CORS 测试页面
      await this.page.goto(`${this.frontendUrl}/cors-test.html`);
      await this.page.waitForLoadState('networkidle');
      
      console.log('✅ CORS 测试页面加载成功');
      
      // 等待页面自动检查服务状态
      await this.page.waitForTimeout(2000);
      
      // 检查服务状态结果
      const serverStatus = await this.page.textContent('#serverStatus');
      console.log('📊 服务状态检查结果:');
      console.log(serverStatus);
      
      return { success: true, serverStatus };
    } catch (error) {
      console.log('❌ CORS 测试页面访问失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  async testCORSPreflight() {
    console.log('\n🌐 测试 CORS 预检请求...');
    
    try {
      // 点击 CORS 预检测试按钮
      await this.page.click('button:has-text("测试 OPTIONS 请求")');
      await this.page.waitForTimeout(3000);
      
      // 获取测试结果
      const corsResult = await this.page.textContent('#corsResult');
      console.log('📋 CORS 预检测试结果:');
      console.log(corsResult);
      
      const isSuccess = corsResult.includes('✅');
      return { success: isSuccess, result: corsResult };
    } catch (error) {
      console.log('❌ CORS 预检测试失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  async testAPIRequests() {
    console.log('\n📡 测试 API 请求...');
    
    try {
      // 测试 GET 请求
      console.log('测试 GET 请求...');
      await this.page.click('button:has-text("GET 请求")');
      await this.page.waitForTimeout(2000);
      
      // 测试 POST 请求
      console.log('测试 POST 请求...');
      await this.page.click('button:has-text("POST 请求")');
      await this.page.waitForTimeout(3000);
      
      // 获取 API 测试结果
      const apiResult = await this.page.textContent('#apiResult');
      console.log('📋 API 请求测试结果:');
      console.log(apiResult);
      
      const isSuccess = apiResult.includes('✅') || apiResult.includes('状态码');
      return { success: isSuccess, result: apiResult };
    } catch (error) {
      console.log('❌ API 请求测试失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  async testViteProxy() {
    console.log('\n🔄 测试 Vite 代理...');
    
    try {
      // 点击代理测试按钮
      await this.page.click('button:has-text("测试代理转发")');
      await this.page.waitForTimeout(3000);
      
      // 获取代理测试结果
      const proxyResult = await this.page.textContent('#proxyResult');
      console.log('📋 Vite 代理测试结果:');
      console.log(proxyResult);
      
      const isSuccess = proxyResult.includes('✅');
      return { success: isSuccess, result: proxyResult };
    } catch (error) {
      console.log('❌ Vite 代理测试失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  async testDirectAPICall() {
    console.log('\n🎯 测试直接 API 调用...');
    
    try {
      // 在页面中执行 JavaScript 进行直接 API 调用
      const result = await this.page.evaluate(async () => {
        try {
          // 测试通过代理的请求
          const proxyResponse = await fetch('/api/dashboard/stats', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
          const proxyData = await proxyResponse.json();
          
          // 测试直接跨域请求
          let directResult = null;
          try {
            const directResponse = await fetch('http://localhost:3000/api/dashboard/stats', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json'
              }
            });
            directResult = await directResponse.json();
          } catch (directError) {
            directResult = { error: directError.message };
          }
          
          return {
            proxy: { success: true, data: proxyData },
            direct: directResult.error ? { success: false, error: directResult.error } : { success: true, data: directResult }
          };
        } catch (error) {
          return { error: error.message };
        }
      });
      
      console.log('📋 直接 API 调用结果:');
      console.log('代理请求:', result.proxy?.success ? '✅ 成功' : '❌ 失败');
      console.log('直接请求:', result.direct?.success ? '✅ 成功' : '❌ 失败');
      
      if (result.proxy?.data) {
        console.log('代理数据:', JSON.stringify(result.proxy.data, null, 2));
      }
      
      return result;
    } catch (error) {
      console.log('❌ 直接 API 调用测试失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  async testLoginFlow() {
    console.log('\n🔐 测试登录流程...');
    
    try {
      // 访问主页面
      await this.page.goto(`${this.frontendUrl}`);
      await this.page.waitForLoadState('networkidle');
      
      // 检查是否重定向到登录页面
      const currentUrl = this.page.url();
      console.log('当前页面:', currentUrl);
      
      if (currentUrl.includes('/login')) {
        console.log('✅ 正确重定向到登录页面');
        
        // 尝试登录（使用测试账号）
        await this.page.fill('input[type="text"], input[type="email"]', '<EMAIL>');
        await this.page.fill('input[type="password"]', 'admin123456');
        
        // 点击登录按钮
        await this.page.click('button:has-text("登录"), button[type="submit"]');
        await this.page.waitForTimeout(3000);
        
        // 检查登录结果
        const newUrl = this.page.url();
        const isLoggedIn = !newUrl.includes('/login');
        
        console.log(isLoggedIn ? '✅ 登录成功' : '❌ 登录失败');
        return { success: isLoggedIn, url: newUrl };
      } else {
        console.log('⚠️  未重定向到登录页面，可能已登录');
        return { success: true, url: currentUrl, alreadyLoggedIn: true };
      }
    } catch (error) {
      console.log('❌ 登录流程测试失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  async runFullTest() {
    console.log('🏥 开始 Playwright CORS 全面测试...\n');
    
    const results = {
      timestamp: new Date().toISOString(),
      tests: {}
    };
    
    try {
      await this.init();
      
      // 1. 测试 CORS 测试页面
      results.tests.corsTestPage = await this.testCORSTestPage();
      
      // 2. 测试 CORS 预检
      results.tests.corsPreflight = await this.testCORSPreflight();
      
      // 3. 测试 API 请求
      results.tests.apiRequests = await this.testAPIRequests();
      
      // 4. 测试 Vite 代理
      results.tests.viteProxy = await this.testViteProxy();
      
      // 5. 测试直接 API 调用
      results.tests.directAPI = await this.testDirectAPICall();
      
      // 6. 测试登录流程
      results.tests.loginFlow = await this.testLoginFlow();
      
      // 生成测试报告
      this.generateReport(results);
      
      return results;
    } finally {
      if (this.browser) {
        console.log('\n🔚 关闭浏览器...');
        await this.browser.close();
      }
    }
  }

  generateReport(results) {
    console.log('\n📊 测试报告');
    console.log('='.repeat(50));
    
    const tests = results.tests;
    let passCount = 0;
    let totalCount = 0;
    
    for (const [testName, result] of Object.entries(tests)) {
      totalCount++;
      const status = result.success ? '✅ 通过' : '❌ 失败';
      const testDisplayName = {
        corsTestPage: 'CORS 测试页面',
        corsPreflight: 'CORS 预检请求',
        apiRequests: 'API 请求测试',
        viteProxy: 'Vite 代理测试',
        directAPI: '直接 API 调用',
        loginFlow: '登录流程测试'
      }[testName] || testName;
      
      console.log(`${status} ${testDisplayName}`);
      
      if (result.success) {
        passCount++;
      } else if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    }
    
    console.log('='.repeat(50));
    console.log(`📈 测试结果: ${passCount}/${totalCount} 通过`);
    
    if (passCount === totalCount) {
      console.log('🎉 所有测试通过！CORS 配置完全正常！');
    } else {
      console.log('⚠️  部分测试失败，请检查相关配置');
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new CORSPlaywrightTester();
  
  // 处理进程退出
  process.on('SIGINT', async () => {
    console.log('\n⏹️  收到退出信号，清理资源...');
    await tester.cleanup();
    process.exit(0);
  });
  
  tester.runFullTest().catch(async (error) => {
    console.error('❌ 测试执行失败:', error);
    await tester.cleanup();
    process.exit(1);
  });
}

export default CORSPlaywrightTester;
