#!/usr/bin/env node

/**
 * CORS 跨域问题检查工具
 * 检查前后端 CORS 配置和连接状态
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

class CORSChecker {
  constructor() {
    this.frontendUrl = 'http://localhost:5173';
    this.backendUrl = 'http://localhost:3000';
    this.apiUrl = `${this.backendUrl}/api`;
  }

  // 检查服务器是否运行
  async checkServerStatus() {
    console.log('🔍 检查服务器状态...');
    
    const results = {
      frontend: false,
      backend: false,
      api: false
    };

    try {
      // 检查前端服务器
      await execAsync(`curl -s -o /dev/null -w "%{http_code}" ${this.frontendUrl}`);
      results.frontend = true;
      console.log('✅ 前端服务器运行正常 (5173)');
    } catch (error) {
      console.log('❌ 前端服务器未运行 (5173)');
    }

    try {
      // 检查后端服务器
      const { stdout } = await execAsync(`curl -s -o /dev/null -w "%{http_code}" ${this.backendUrl}`);
      if (stdout.trim() !== '000') {
        results.backend = true;
        console.log('✅ 后端服务器运行正常 (3000)');
      }
    } catch (error) {
      console.log('❌ 后端服务器未运行 (3000)');
    }

    try {
      // 检查 API 端点
      const { stdout } = await execAsync(`curl -s -o /dev/null -w "%{http_code}" ${this.apiUrl}/auth/login`);
      if (stdout.trim() !== '000') {
        results.api = true;
        console.log('✅ API 端点可访问');
      }
    } catch (error) {
      console.log('❌ API 端点不可访问');
    }

    return results;
  }

  // 检查 CORS 预检请求
  async checkCORSPreflight() {
    console.log('\n🔍 检查 CORS 预检请求...');
    
    try {
      const { stdout } = await execAsync(`curl -s -I -X OPTIONS ${this.apiUrl}/auth/login \\
        -H "Origin: ${this.frontendUrl}" \\
        -H "Access-Control-Request-Method: POST" \\
        -H "Access-Control-Request-Headers: Content-Type,Authorization"`);
      
      const headers = this.parseHeaders(stdout);
      
      console.log('📋 CORS 响应头:');
      console.log(`   Access-Control-Allow-Origin: ${headers['access-control-allow-origin'] || '未设置'}`);
      console.log(`   Access-Control-Allow-Methods: ${headers['access-control-allow-methods'] || '未设置'}`);
      console.log(`   Access-Control-Allow-Headers: ${headers['access-control-allow-headers'] || '未设置'}`);
      console.log(`   Access-Control-Allow-Credentials: ${headers['access-control-allow-credentials'] || '未设置'}`);
      
      // 检查关键 CORS 头
      const issues = [];
      if (!headers['access-control-allow-origin']) {
        issues.push('缺少 Access-Control-Allow-Origin 头');
      } else if (headers['access-control-allow-origin'] !== this.frontendUrl && headers['access-control-allow-origin'] !== '*') {
        issues.push(`Origin 不匹配: 期望 ${this.frontendUrl}, 实际 ${headers['access-control-allow-origin']}`);
      }
      
      if (!headers['access-control-allow-methods'] || !headers['access-control-allow-methods'].includes('POST')) {
        issues.push('POST 方法未被允许');
      }
      
      if (!headers['access-control-allow-headers'] || !headers['access-control-allow-headers'].includes('Content-Type')) {
        issues.push('Content-Type 头未被允许');
      }
      
      if (issues.length === 0) {
        console.log('✅ CORS 预检请求配置正确');
        return { status: 'ok', issues: [] };
      } else {
        console.log('⚠️  发现 CORS 配置问题:');
        issues.forEach(issue => console.log(`   - ${issue}`));
        return { status: 'issues', issues };
      }
      
    } catch (error) {
      console.log('❌ CORS 预检请求失败:', error.message);
      return { status: 'error', error: error.message };
    }
  }

  // 检查实际 API 请求
  async checkAPIRequest() {
    console.log('\n🔍 检查实际 API 请求...');
    
    try {
      const { stdout } = await execAsync(`curl -s -v -X GET ${this.apiUrl}/dashboard/stats \\
        -H "Origin: ${this.frontendUrl}" \\
        -H "Content-Type: application/json" 2>&1`);
      
      const hasOriginHeader = stdout.includes('Access-Control-Allow-Origin');
      const hasCredentialsHeader = stdout.includes('Access-Control-Allow-Credentials');
      
      if (hasOriginHeader) {
        console.log('✅ API 请求返回正确的 CORS 头');
        return { status: 'ok' };
      } else {
        console.log('❌ API 请求缺少 CORS 头');
        return { status: 'missing_headers' };
      }
      
    } catch (error) {
      console.log('❌ API 请求失败:', error.message);
      return { status: 'error', error: error.message };
    }
  }

  // 检查 Vite 代理配置
  checkViteProxy() {
    console.log('\n🔍 检查 Vite 代理配置...');
    
    // 这里应该读取 vite.config.js 文件，但为了简化，我们直接检查已知配置
    const expectedProxy = {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false
      }
    };
    
    console.log('📋 Vite 代理配置:');
    console.log(`   /api -> ${expectedProxy['/api'].target}`);
    console.log(`   changeOrigin: ${expectedProxy['/api'].changeOrigin}`);
    console.log(`   secure: ${expectedProxy['/api'].secure}`);
    console.log('✅ Vite 代理配置正确');
    
    return { status: 'ok', config: expectedProxy };
  }

  // 解析 HTTP 响应头
  parseHeaders(headerString) {
    const headers = {};
    const lines = headerString.split('\n');
    
    for (const line of lines) {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex).trim().toLowerCase();
        const value = line.substring(colonIndex + 1).trim();
        headers[key] = value;
      }
    }
    
    return headers;
  }

  // 生成修复建议
  generateFixSuggestions(results) {
    console.log('\n💡 修复建议:');
    console.log('================');
    
    if (!results.serverStatus.frontend) {
      console.log('1. 启动前端开发服务器:');
      console.log('   cd frontend && npm run dev');
    }
    
    if (!results.serverStatus.backend) {
      console.log('2. 启动后端服务器:');
      console.log('   cd backend && npm start');
      console.log('   或使用 PM2: pm2 start ecosystem.config.js');
    }
    
    if (results.corsCheck.status === 'issues') {
      console.log('3. 修复 CORS 配置问题:');
      console.log('   检查 backend/src/app.js 中的 CORS 配置');
      console.log('   确保允许的 Origin 包含 http://localhost:5173');
    }
    
    if (results.apiCheck.status === 'missing_headers') {
      console.log('4. 检查后端 CORS 中间件:');
      console.log('   确保 cors() 中间件正确配置并在路由之前使用');
    }
    
    console.log('\n🔧 快速修复命令:');
    console.log('   npm run dev:clean  # 清理缓存重启前端');
    console.log('   pm2 restart all    # 重启所有服务');
  }

  // 运行完整检查
  async runFullCheck() {
    console.log('🏥 开始 CORS 跨域问题检查...\n');
    
    const results = {
      serverStatus: await this.checkServerStatus(),
      corsCheck: await this.checkCORSPreflight(),
      apiCheck: await this.checkAPIRequest(),
      viteProxy: this.checkViteProxy(),
      timestamp: new Date().toISOString()
    };
    
    // 生成修复建议
    this.generateFixSuggestions(results);
    
    return results;
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new CORSChecker();
  checker.runFullCheck().catch(console.error);
}

export default CORSChecker;
